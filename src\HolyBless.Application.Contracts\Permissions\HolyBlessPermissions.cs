namespace HolyBless.Permissions
{
    public static class HolyBlessPermissions
    {
        public const string GroupName = "HolyBless";

        public static class EBooks
        {
            public const string Default = GroupName + ".EBooks";
            public const string Create = Default + ".Create";
            public const string Edit = Default + ".Edit";
            public const string Delete = Default + ".Delete";
        }

        public static class StorageProviders
        {
            public const string Default = GroupName + ".StorageProviders";
            public const string Create = $"{Default}.Create";
            public const string Edit = $"{Default}.Edit";
            public const string Delete = $"{Default}.Delete";
        }

        public static class ProviderSecrets
        {
            public const string Default = GroupName + ".ProviderSecrets";
            public const string Create = $"{Default}.Create";
            public const string Edit = $"{Default}.Edit";
            public const string Delete = $"{Default}.Delete";
        }

        public static class StorageBuckets
        {
            public const string Default = GroupName + ".StorageBuckets";
            public const string Create = $"{Default}.Create";
            public const string Edit = $"{Default}.Edit";
            public const string Delete = $"{Default}.Delete";
        }

        public static class BucketFiles
        {
            public const string Default = GroupName + ".BucketFiles";
            public const string Create = $"{Default}.Create";
            public const string Edit = $"{Default}.Edit";
            public const string Delete = $"{Default}.Delete";
        }

        public static class Countries
        {
            public const string Default = GroupName + ".Countries";
            public const string Create = $"{Default}.Create";
            public const string Edit = $"{Default}.Edit";
            public const string Delete = $"{Default}.Delete";
        }

        public static class Collections
        {
            public const string Default = GroupName + ".Collections";
            public const string Create = $"{Default}.Create";
            public const string Edit = $"{Default}.Edit";
            public const string Delete = $"{Default}.Delete";
        }

        public static class CollectionArticles
        {
            public const string Default = GroupName + ".CollectionArticles";
            public const string Create = $"{Default}.Create";
            public const string Edit = $"{Default}.Edit";
            public const string Delete = $"{Default}.Delete";
        }

        public static class Channels
        {
            public const string Default = GroupName + ".Channels";
            public const string Create = $"{Default}.Create";
            public const string Edit = $"{Default}.Edit";
            public const string Delete = $"{Default}.Delete";
        }

        public static class Articles
        {
            public const string Default = "HolyBless.Articles";
            public const string Create = Default + ".Create";
            public const string Edit = Default + ".Edit";
            public const string Delete = Default + ".Delete";
        }

        public static class Tags
        {
            public const string Default = GroupName + ".Tags";
            public const string Create = Default + ".Create";
            public const string Edit = Default + ".Edit";
            public const string Delete = Default + ".Delete";
        }

        public static class ArticleFiles
        {
            public const string Default = GroupName + ".ArticleFiles";
            public const string Create = Default + ".Create";
            public const string Edit = Default + ".Edit";
            public const string Delete = Default + ".Delete";
        }

        public static class Albums
        {
            public const string Default = GroupName + ".Albums";
            public const string Create = Default + ".Create";
            public const string Edit = Default + ".Edit";
            public const string Delete = Default + ".Delete";
        }

        public static class VirtualFolders
        {
            public const string Default = GroupName + ".VirtualFolders";
            public const string Create = Default + ".Create";
            public const string Edit = Default + ".Edit";
            public const string Delete = Default + ".Delete";
        }
    }
}