using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Articles;
using HolyBless.Articles.Dtos;
using HolyBless.Collections.Dtos;
using HolyBless.Enums;
using Shouldly;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace HolyBless.Collections
{
    public abstract class CollectionArticleAppService_Tests<TStartupModule> : HolyBlessApplicationTestBase<TStartupModule>
            where TStartupModule : IAbpModule
    {
        private readonly ICollectionArticleAppService _collectionArticleAppService;
        private readonly ICollectionAppService _collectionAppService;
        private readonly IArticleAppService _articleAppService;

        protected CollectionArticleAppService_Tests()
        {
            _collectionArticleAppService = GetRequiredService<ICollectionArticleAppService>();
            _collectionAppService = GetRequiredService<ICollectionAppService>();
            _articleAppService = GetRequiredService<IArticleAppService>();
        }

        [Fact]
        public async Task Should_Create_Collection_Article_Relationship()
        {
            // Arrange
            var collection = await CreateTestCollectionAsync();
            var article = await CreateTestArticleAsync();

            var input = new CreateUpdateCollectionToArticleDto
            {
                CollectionId = collection.Id,
                ArticleId = article.Id,
                Weight = 10
            };

            // Act
            var result = await _collectionArticleAppService.CreateAsync(input);

            // Assert
            result.ShouldNotBeNull();
            result.CollectionId.ShouldBe(collection.Id);
            result.ArticleId.ShouldBe(article.Id);
            result.Weight.ShouldBe(10);
            result.CollectionName.ShouldBe(collection.Name);
            result.ArticleTitle.ShouldBe(article.Title);
        }

        [Fact]
        public async Task Should_Not_Create_Duplicate_Collection_Article_Relationship()
        {
            // Arrange
            var collection = await CreateTestCollectionAsync();
            var article = await CreateTestArticleAsync();

            var input = new CreateUpdateCollectionToArticleDto
            {
                CollectionId = collection.Id,
                ArticleId = article.Id,
                Weight = 10
            };

            // Create first relationship
            await _collectionArticleAppService.CreateAsync(input);

            // Act & Assert
            await Should.ThrowAsync<UserFriendlyException>(async () =>
            {
                await _collectionArticleAppService.CreateAsync(input);
            });
        }

        [Fact]
        public async Task Should_Get_Collection_Article_Relationship()
        {
            // Arrange
            var collection = await CreateTestCollectionAsync();
            var article = await CreateTestArticleAsync();

            var input = new CreateUpdateCollectionToArticleDto
            {
                CollectionId = collection.Id,
                ArticleId = article.Id,
                Weight = 15
            };

            await _collectionArticleAppService.CreateAsync(input);

            // Act
            var result = await _collectionArticleAppService.GetAsync(new CollectionToArticleKey(collection.Id, article.Id));

            // Assert
            result.ShouldNotBeNull();
            result.CollectionId.ShouldBe(collection.Id);
            result.ArticleId.ShouldBe(article.Id);
            result.Weight.ShouldBe(15);
        }

        [Fact]
        public async Task Should_Update_Collection_Article_Weight()
        {
            // Arrange
            var collection = await CreateTestCollectionAsync();
            var article = await CreateTestArticleAsync();

            var input = new CreateUpdateCollectionToArticleDto
            {
                CollectionId = collection.Id,
                ArticleId = article.Id,
                Weight = 20
            };

            await _collectionArticleAppService.CreateAsync(input);

            // Act
            var result = await _collectionArticleAppService.UpdateWeightAsync(collection.Id, article.Id, 50);

            // Assert
            result.ShouldNotBeNull();
            result.Weight.ShouldBe(50);
        }

        [Fact]
        public async Task Should_Delete_Collection_Article_Relationship()
        {
            // Arrange
            var collection = await CreateTestCollectionAsync();
            var article = await CreateTestArticleAsync();

            var input = new CreateUpdateCollectionToArticleDto
            {
                CollectionId = collection.Id,
                ArticleId = article.Id,
                Weight = 25
            };

            await _collectionArticleAppService.CreateAsync(input);

            // Act
            await _collectionArticleAppService.DeleteAsync(new CollectionToArticleKey(collection.Id, article.Id));

            // Assert
            await Should.ThrowAsync<EntityNotFoundException>(async () =>
            {
                await _collectionArticleAppService.GetAsync(new CollectionToArticleKey(collection.Id, article.Id));
            });
        }

        [Fact]
        public async Task Should_Get_Articles_By_Collection_Id()
        {
            // Arrange
            var collection = await CreateTestCollectionAsync();
            var article1 = await CreateTestArticleAsync("Test Article 1");
            var article2 = await CreateTestArticleAsync("Test Article 2");

            await _collectionArticleAppService.CreateAsync(new CreateUpdateCollectionToArticleDto
            {
                CollectionId = collection.Id,
                ArticleId = article1.Id,
                Weight = 10
            });

            await _collectionArticleAppService.CreateAsync(new CreateUpdateCollectionToArticleDto
            {
                CollectionId = collection.Id,
                ArticleId = article2.Id,
                Weight = 20
            });

            // Act
            var result = await _collectionArticleAppService.GetByCollectionIdAsync(collection.Id);

            // Assert
            result.ShouldNotBeNull();
            result.Count.ShouldBe(2);
            result.ShouldContain(r => r.ArticleId == article1.Id);
            result.ShouldContain(r => r.ArticleId == article2.Id);
        }

        [Fact]
        public async Task Should_Check_If_Relationship_Exists()
        {
            // Arrange
            var collection = await CreateTestCollectionAsync();
            var article = await CreateTestArticleAsync();

            // Act - Check before creation
            var existsBefore = await _collectionArticleAppService.ExistsAsync(collection.Id, article.Id);

            // Create relationship
            await _collectionArticleAppService.CreateAsync(new CreateUpdateCollectionToArticleDto
            {
                CollectionId = collection.Id,
                ArticleId = article.Id,
                Weight = 30
            });

            // Act - Check after creation
            var existsAfter = await _collectionArticleAppService.ExistsAsync(collection.Id, article.Id);

            // Assert
            existsBefore.ShouldBeFalse();
            existsAfter.ShouldBeTrue();
        }

        private async Task<CollectionDto> CreateTestCollectionAsync()
        {
            return await _collectionAppService.CreateAsync(new CreateUpdateCollectionDto
            {
                Name = $"Test Collection {Guid.NewGuid()}",
                ContentCode = "TEST",
                LanguageCode = "EN",
                Description = "Test collection for unit tests",
                Status = PublishStatus.Published
            });
        }

        private async Task<ArticleDto> CreateTestArticleAsync(string title = null)
        {
            return await _articleAppService.CreateAsync(new CreateUpdateArticleDto
            {
                Title = title ?? $"Test Article {Guid.NewGuid()}",
                LanguageCode = "EN",
                DeliveryDate = DateTime.Now,
                Description = "Test article for unit tests",
                ArticleContentCategory = ArticleContentCategory.Article,
                Status = PublishStatus.Published
            });
        }
    }
}
