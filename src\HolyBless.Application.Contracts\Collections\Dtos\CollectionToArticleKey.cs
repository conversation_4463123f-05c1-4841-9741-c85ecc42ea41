using System;

namespace HolyBless.Collections.Dtos
{
    public class CollectionToArticleKey : IEquatable<CollectionToArticleKey>
    {
        public int CollectionId { get; set; }
        public int ArticleId { get; set; }

        public CollectionToArticleKey()
        {
        }

        public CollectionToArticleKey(int collectionId, int articleId)
        {
            CollectionId = collectionId;
            ArticleId = articleId;
        }

        public bool Equals(CollectionToArticleKey? other)
        {
            if (other is null) return false;
            if (ReferenceEquals(this, other)) return true;
            return CollectionId == other.CollectionId && ArticleId == other.ArticleId;
        }

        public override bool Equals(object? obj)
        {
            return Equals(obj as CollectionToArticleKey);
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(CollectionId, ArticleId);
        }

        public override string ToString()
        {
            return $"{CollectionId}-{ArticleId}";
        }

        public static bool operator ==(CollectionToArticleKey? left, CollectionToArticleKey? right)
        {
            return Equals(left, right);
        }

        public static bool operator !=(CollectionToArticleKey? left, CollectionToArticleKey? right)
        {
            return !Equals(left, right);
        }
    }
}
