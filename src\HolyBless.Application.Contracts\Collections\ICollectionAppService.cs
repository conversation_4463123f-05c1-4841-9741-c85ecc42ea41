﻿using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Collections.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Collections
{
    public interface ICollectionAppService : IReadOnlyCollectionAppService
    {
        Task<CollectionDto> CreateAsync(CreateUpdateCollectionDto input);
        Task<CollectionDto> UpdateAsync(int id, CreateUpdateCollectionDto input);
        Task DeleteAsync(int id);
        Task<PagedResultDto<CollectionDto>> GetListAsync(CollectionSearchDto input);
        Task AddFilesToCollection(int collectionId, List<CollectionToFileDto> files);

        Task<PagedResultDto<CollectionArticleDto>> GetCollectionArticlesAsync(CollectionArticleSearchDto input);

        Task<PagedResultDto<CollectionToFileDto>> GetCollectionFilesAsync(CollectionFileSearchDto input);

        Task LinkToChannel(int channelId, int collectionId);

        Task LinkToChannel(int channelId, List<int> collectionIds);

        Task UnlinkFromChannel(int collectionId);

        Task UnlinkFromChannel(List<int> collectionIds);

        Task<List<CollectionDto>> GetAllCollectionsAsync(string? languageCode = null);
        Task MoveCollectionAsync(int collectionId, int? toParentId, int? beforeId);
    }
}