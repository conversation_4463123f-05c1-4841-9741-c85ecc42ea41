using System.Collections.Generic;
using System.Threading.Tasks;
using HolyBless.Collections.Dtos;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace HolyBless.Collections
{
    public interface ICollectionArticleAppService : IApplicationService
    {
        Task<CollectionToArticleDetailDto> GetAsync(CollectionToArticleKey id);
        Task<CollectionToArticleDetailDto> CreateAsync(CreateUpdateCollectionToArticleDto input);
        Task DeleteAsync(CollectionToArticleKey id);
        Task<List<CollectionToArticleDetailDto>> GetByCollectionIdAsync(int collectionId);
        Task<List<CollectionToArticleDetailDto>> GetByArticleIdAsync(int articleId);
      
        Task<CollectionToArticleDetailDto> UpdateWeightAsync(int collectionId, int articleId, int weight);
        Task<bool> ExistsAsync(int collectionId, int articleId);
        Task AddArticlesToCollection(int collectionId, List<CollectionArticleDto> articles);
        Task AddArticlesToCollection(int collectionId, List<int> articleIds);
        Task RemoveArticlesFromCollection(int collectionId, List<int> articleIds);
    }
}
