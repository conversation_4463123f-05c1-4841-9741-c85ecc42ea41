using HolyBless.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace HolyBless.Permissions;

public class HolyBlessPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(HolyBlessPermissions.GroupName);

        var booksPermission = myGroup.AddPermission(HolyBlessPermissions.EBooks.Default, L("Permission:Books"));
        booksPermission.AddChild(HolyBlessPermissions.EBooks.Create, L("Permission:Books.Create"));
        booksPermission.AddChild(HolyBlessPermissions.EBooks.Edit, L("Permission:Books.Edit"));
        booksPermission.AddChild(HolyBlessPermissions.EBooks.Delete, L("Permission:Books.Delete"));
        //Define your own permissions here. Example:
        var articlePermission = myGroup.AddPermission(HolyBlessPermissions.Articles.Default, L("Permission:Articles"));
        articlePermission.AddChild(HolyBlessPermissions.Articles.Create, L("Permission:Articles.Create"));
        articlePermission.AddChild(HolyBlessPermissions.Articles.Edit, L("Permission:Articles.Edit"));
        articlePermission.AddChild(HolyBlessPermissions.Articles.Delete, L("Permission:Articles.Delete"));

        var tagPermission = myGroup.AddPermission(HolyBlessPermissions.Tags.Default, L("Permission:Tags"));
        tagPermission.AddChild(HolyBlessPermissions.Tags.Create, L("Permission:Tags.Create"));
        tagPermission.AddChild(HolyBlessPermissions.Tags.Edit, L("Permission:Tags.Edit"));
        tagPermission.AddChild(HolyBlessPermissions.Tags.Delete, L("Permission:Tags.Delete"));

        var articleFilePermission = myGroup.AddPermission(HolyBlessPermissions.ArticleFiles.Default, L("Permission:ArticleFiles"));
        articleFilePermission.AddChild(HolyBlessPermissions.ArticleFiles.Create, L("Permission:ArticleFiles.Create"));
        articleFilePermission.AddChild(HolyBlessPermissions.ArticleFiles.Edit, L("Permission:ArticleFiles.Edit"));
        articleFilePermission.AddChild(HolyBlessPermissions.ArticleFiles.Delete, L("Permission:ArticleFiles.Delete"));

        var storageBucketsPermission = myGroup.AddPermission(HolyBlessPermissions.StorageBuckets.Default, L("Permission:StorageBuckets"));
        storageBucketsPermission.AddChild(HolyBlessPermissions.StorageBuckets.Create, L("Permission:StorageBuckets.Create"));
        storageBucketsPermission.AddChild(HolyBlessPermissions.StorageBuckets.Edit, L("Permission:StorageBuckets.Edit"));
        storageBucketsPermission.AddChild(HolyBlessPermissions.StorageBuckets.Delete, L("Permission:StorageBuckets.Delete"));

        var storageProvidersPermission = myGroup.AddPermission(HolyBlessPermissions.StorageProviders.Default, L("Permission:StorageProviders"));
        storageProvidersPermission.AddChild(HolyBlessPermissions.StorageProviders.Create, L("Permission:StorageProviders.Create"));
        storageProvidersPermission.AddChild(HolyBlessPermissions.StorageProviders.Edit, L("Permission:StorageProviders.Edit"));
        storageProvidersPermission.AddChild(HolyBlessPermissions.StorageProviders.Delete, L("Permission:StorageProviders.Delete"));

        var providerSecretsPermission = myGroup.AddPermission(HolyBlessPermissions.ProviderSecrets.Default, L("Permission:ProviderSecrets"));
        providerSecretsPermission.AddChild(HolyBlessPermissions.ProviderSecrets.Create, L("Permission:ProviderSecrets.Create"));
        providerSecretsPermission.AddChild(HolyBlessPermissions.ProviderSecrets.Edit, L("Permission:ProviderSecrets.Edit"));
        providerSecretsPermission.AddChild(HolyBlessPermissions.ProviderSecrets.Delete, L("Permission:ProviderSecrets.Delete"));

        var bucketFilesPermission = myGroup.AddPermission(HolyBlessPermissions.BucketFiles.Default, L("Permission:BucketFiles"));
        bucketFilesPermission.AddChild(HolyBlessPermissions.BucketFiles.Create, L("Permission:BucketFiles.Create"));
        bucketFilesPermission.AddChild(HolyBlessPermissions.BucketFiles.Edit, L("Permission:BucketFiles.Edit"));
        bucketFilesPermission.AddChild(HolyBlessPermissions.BucketFiles.Delete, L("Permission:BucketFiles.Delete"));

        var countriesPermission = myGroup.AddPermission(HolyBlessPermissions.Countries.Default, L("Permission:Countries"));
        countriesPermission.AddChild(HolyBlessPermissions.Countries.Create, L("Permission:Countries.Create"));
        countriesPermission.AddChild(HolyBlessPermissions.Countries.Edit, L("Permission:Countries.Edit"));
        countriesPermission.AddChild(HolyBlessPermissions.Countries.Delete, L("Permission:Countries.Delete"));

        var collectionsPermission = myGroup.AddPermission(HolyBlessPermissions.Collections.Default, L("Permission:Collections"));
        collectionsPermission.AddChild(HolyBlessPermissions.Collections.Create, L("Permission:Collections.Create"));
        collectionsPermission.AddChild(HolyBlessPermissions.Collections.Edit, L("Permission:Collections.Edit"));
        collectionsPermission.AddChild(HolyBlessPermissions.Collections.Delete, L("Permission:Collections.Delete"));

        var collectionArticlesPermission = myGroup.AddPermission(HolyBlessPermissions.CollectionArticles.Default, L("Permission:CollectionArticles"));
        collectionArticlesPermission.AddChild(HolyBlessPermissions.CollectionArticles.Create, L("Permission:CollectionArticles.Create"));
        collectionArticlesPermission.AddChild(HolyBlessPermissions.CollectionArticles.Edit, L("Permission:CollectionArticles.Edit"));
        collectionArticlesPermission.AddChild(HolyBlessPermissions.CollectionArticles.Delete, L("Permission:CollectionArticles.Delete"));

        var channelsPermission = myGroup.AddPermission(HolyBlessPermissions.Channels.Default, L("Permission:Channels"));
        channelsPermission.AddChild(HolyBlessPermissions.Channels.Create, L("Permission:Channels.Create"));
        channelsPermission.AddChild(HolyBlessPermissions.Channels.Edit, L("Permission:Channels.Edit"));
        channelsPermission.AddChild(HolyBlessPermissions.Channels.Delete, L("Permission:Channels.Delete"));

        var albumsPermission = myGroup.AddPermission(HolyBlessPermissions.Albums.Default, L("Permission:Albums"));
        albumsPermission.AddChild(HolyBlessPermissions.Albums.Create, L("Permission:Albums.Create"));
        albumsPermission.AddChild(HolyBlessPermissions.Albums.Edit, L("Permission:Albums.Edit"));
        albumsPermission.AddChild(HolyBlessPermissions.Albums.Delete, L("Permission:Albums.Delete"));

        var folderPermission = myGroup.AddPermission(HolyBlessPermissions.VirtualFolders.Default, L("Permission:VirtualFolders"));
        folderPermission.AddChild(HolyBlessPermissions.VirtualFolders.Create, L("Permission:VirtualFolders.Create"));
        folderPermission.AddChild(HolyBlessPermissions.VirtualFolders.Edit, L("Permission:VirtualFolders.Edit"));
        folderPermission.AddChild(HolyBlessPermissions.VirtualFolders.Delete, L("Permission:VirtualFolders.Delete"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<HolyBlessResource>(name);
    }
}