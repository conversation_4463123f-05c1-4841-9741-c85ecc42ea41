using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Collections.Dtos;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Collections;
using HolyBless.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using System.Linq.Dynamic.Core;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using System.ComponentModel.DataAnnotations;

namespace HolyBless.Collections
{
    [Authorize(HolyBlessPermissions.CollectionArticles.Default)]
    public class CollectionArticleAppService : HolyBlessAppService, ICollectionArticleAppService
    {
        private readonly IRepository<CollectionToArticle> _repository;

        public CollectionArticleAppService(
            IRepository<CollectionToArticle> repository
            )
        {
            _repository = repository;
        }

        [Authorize(HolyBlessPermissions.CollectionArticles.Create)]
        public async Task<CollectionToArticleDetailDto> CreateAsync(CreateUpdateCollectionToArticleDto input)
        {
            var query = await _repository.GetQueryableAsync();
            var exists = await query.AnyAsync(ca => ca.CollectionId == input.CollectionId && ca.ArticleId == input.ArticleId);
            if (exists)
            {
                throw new UserFriendlyException(L["ArticleAlreadyLinkedToCollection"]);
            }
            if (input.Weight == null)
            {
                var maxId = await query.Where(x => x.CollectionId == input.CollectionId).MaxAsync(x => x.Weight);
                input.Weight = maxId + 1;
            }
            var collectionToArticle = ObjectMapper.Map<CreateUpdateCollectionToArticleDto, CollectionToArticle>(input);
            collectionToArticle = await _repository.InsertAsync(collectionToArticle, autoSave: true);

            return await GetAsync(new CollectionToArticleKey(input.CollectionId, input.ArticleId));
        }

        /*[Authorize(HolyBlessPermissions.CollectionArticles.Edit)]
                [Authorize(HolyBlessPermissions.CollectionArticles.Edit)]
        public async Task<CollectionToArticleDetailDto> UpdateAsync(CollectionToArticleKey id, CreateUpdateCollectionToArticleDto input)
        {
            var collectionToArticle = await GetEntityAsync(id);

            if (collectionToArticle.CollectionId != input.CollectionId || collectionToArticle.ArticleId != input.ArticleId)
            {
                await ValidateCollectionAndArticleExistAsync(input.CollectionId, input.ArticleId);
                var query = await _repository.GetQueryableAsync();
                var exists = await query.AnyAsync(ca =>
                    ca.CollectionId == input.CollectionId &&
                    ca.ArticleId == input.ArticleId &&
                    !(ca.CollectionId == id.CollectionId && ca.ArticleId == id.ArticleId));
                if (exists)
                {
                    throw new UserFriendlyException(L["ArticleAlreadyLinkedToCollection"]);
                }
            }

            ObjectMapper.Map(input, collectionToArticle);
            collectionToArticle = await _repository.UpdateAsync(collectionToArticle, autoSave: true);

            return await GetAsync(new CollectionToArticleKey(input.CollectionId, input.ArticleId));
        }
        */

        /// <summary>
        /// [Admin] Get a specific CollectionToArticle by its composite key
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        /// <exception cref="EntityNotFoundException"></exception>
        public async Task<CollectionToArticleDetailDto> GetAsync(CollectionToArticleKey id)
        {
            var query = await _repository.GetQueryableAsync();
            var collectionToArticle = await query
                .Include(ca => ca.Collection)
                .Include(ca => ca.Article)
                .FirstOrDefaultAsync(ca => ca.CollectionId == id.CollectionId && ca.ArticleId == id.ArticleId);

            if (collectionToArticle == null)
            {
                throw new EntityNotFoundException(typeof(CollectionToArticle), $"{id.CollectionId}-{id.ArticleId}");
            }

            return ObjectMapper.Map<CollectionToArticle, CollectionToArticleDetailDto>(collectionToArticle);
        }

        public async Task<PagedResultDto<CollectionToArticleDetailDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var query = await _repository.GetQueryableAsync();

            var queryable = query
                .Include(ca => ca.Collection)
                .Include(ca => ca.Article)
                .OrderBy(input.Sorting ?? "Weight");

            var collectionToArticles = await AsyncExecuter.ToListAsync(
                queryable.Skip(input.SkipCount).Take(input.MaxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<CollectionToArticleDetailDto>(
                totalCount,
                ObjectMapper.Map<List<CollectionToArticle>, List<CollectionToArticleDetailDto>>(collectionToArticles)
            );
        }

        [Authorize(HolyBlessPermissions.CollectionArticles.Delete)]
        public async Task DeleteAsync(CollectionToArticleKey id)
        {
            var collectionToArticle = await GetEntityAsync(id);
            await _repository.DeleteAsync(collectionToArticle);
        }

        public async Task<List<CollectionToArticleDetailDto>> GetByCollectionIdAsync(int collectionId)
        {
            var query = await _repository.GetQueryableAsync();
            var collectionToArticles = await query
                .Include(ca => ca.Collection)
                .Include(ca => ca.Article)
                .Where(ca => ca.CollectionId == collectionId)
                .OrderBy(ca => ca.Weight)
                .ToListAsync();

            return ObjectMapper.Map<List<CollectionToArticle>, List<CollectionToArticleDetailDto>>(collectionToArticles);
        }

        public async Task<List<CollectionToArticleDetailDto>> GetByArticleIdAsync(int articleId)
        {
            var query = await _repository.GetQueryableAsync();
            var collectionToArticles = await query
                .Include(ca => ca.Collection)
                .Include(ca => ca.Article)
                .Where(ca => ca.ArticleId == articleId)
                .OrderBy(ca => ca.Weight)
                .ToListAsync();

            return ObjectMapper.Map<List<CollectionToArticle>, List<CollectionToArticleDetailDto>>(collectionToArticles);
        }

        [Authorize(HolyBlessPermissions.CollectionArticles.Edit)]
        public async Task<CollectionToArticleDetailDto> UpdateWeightAsync(int collectionId, int articleId, int weight)
        {
            var collectionToArticle = await GetEntityAsync(new CollectionToArticleKey(collectionId, articleId));
            collectionToArticle.Weight = weight;
            await _repository.UpdateAsync(collectionToArticle, autoSave: true);

            return await GetAsync(new CollectionToArticleKey(collectionId, articleId));
        }

        public async Task<bool> ExistsAsync(int collectionId, int articleId)
        {
            var query = await _repository.GetQueryableAsync();
            return await query.AnyAsync(ca => ca.CollectionId == collectionId && ca.ArticleId == articleId);
        }

        [Authorize(HolyBlessPermissions.CollectionArticles.Create)]
        public async Task AddArticlesToCollection(int collectionId, List<CollectionArticleDto> articles)
        {
            var query = await _repository.GetQueryableAsync();
            var maxId = await query.Where(x => x.CollectionId == collectionId).MaxAsync(x => x.Weight);
            var newList = new List<CollectionToArticle>();
            foreach (var articleDto in articles)
            {
                var existing = await query.FirstOrDefaultAsync(x => x.CollectionId == collectionId && x.ArticleId == articleDto.ArticleId);
                if (existing != null) continue;

                newList.Add(new CollectionToArticle
                {
                    CollectionId = collectionId,
                    ArticleId = articleDto.ArticleId,
                    Weight = articleDto.Weight ?? (++maxId)
                });
            }
            if (newList.Count > 0)
            {
                await _repository.InsertManyAsync(newList, true);
            }
        }

        [Authorize(HolyBlessPermissions.CollectionArticles.Create)]
        public async Task AddArticlesToCollection(int collectionId, List<int> articleIds)
        {
            var dtoList = articleIds.Select(id => new CollectionArticleDto { ArticleId = id }).ToList();
            await AddArticlesToCollection(collectionId, dtoList);
        }
        private async Task<CollectionToArticle> GetEntityAsync(CollectionToArticleKey id)
        {
            var query = await _repository.GetQueryableAsync();
            var collectionToArticle = await query
                .FirstOrDefaultAsync(ca => ca.CollectionId == id.CollectionId && ca.ArticleId == id.ArticleId);

            if (collectionToArticle == null)
            {
                throw new EntityNotFoundException(typeof(CollectionToArticle), $"{id.CollectionId}-{id.ArticleId}");
            }

            return collectionToArticle;
        }

        [Authorize(HolyBlessPermissions.CollectionArticles.Delete)]
        public async Task RemoveArticlesFromCollection(int collectionId, List<int> articleIds)
        {
            var query = await _repository.GetQueryableAsync();
            var entities = await query
                .Where(ca => ca.CollectionId == collectionId && articleIds.Contains(ca.ArticleId))
                .ToListAsync();
            await _repository.DeleteManyAsync(entities, true);
        }
    }
}