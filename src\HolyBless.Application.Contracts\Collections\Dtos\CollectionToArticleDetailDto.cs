using System;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Collections.Dtos
{
    public class CollectionToArticleDetailDto : EntityDto<CollectionToArticleKey>
    {
        public int CollectionId { get; set; }
        public string CollectionName { get; set; } = string.Empty;
        
        public int ArticleId { get; set; }
        public string ArticleTitle { get; set; } = string.Empty;
        
        public int Weight { get; set; }
        
    }
}
