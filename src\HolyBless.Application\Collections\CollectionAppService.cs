﻿using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Buckets;
using HolyBless.Collections.Dtos;
using HolyBless.Configs;
using HolyBless.Entities.Collections;
using HolyBless.Interfaces;
using HolyBless.Permissions;
using HolyBless.Services;
using HolyBless.TreeJsonSnapshots;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.Collections
{
    [Authorize]
    public class CollectionAppService : ReadOnlyCollectionAppService, ICollectionAppService
    {
        public CollectionAppService(
            ICollectionRepository repository,
            AppConfig settings,
            IRepository<CollectionToArticle> collectionToArticleRepository,
            IRepository<CollectionToFile> collectionToFileRepository,
            ITreeJsonSnapshotAppService treeSnapshotAppService,
            IRequestContextService requestContextService,
            ICachedFileUrlAppService cachedFileUrlAppService
            )
            : base(repository, settings, collectionToArticleRepository, collectionToFileRepository, treeSnapshotAppService, requestContextService, cachedFileUrlAppService)
        {
        }

        [Authorize(HolyBlessPermissions.Collections.Create)]
        public async Task<CollectionDto> CreateAsync(CreateUpdateCollectionDto input)
        {
            var collection = ObjectMapper.Map<CreateUpdateCollectionDto, Collection>(input);
            collection = await _repository.InsertAsync(collection, autoSave: true);
            return ObjectMapper.Map<Collection, CollectionDto>(collection);
        }

        [Authorize(HolyBlessPermissions.Collections.Edit)]
        public async Task<CollectionDto> UpdateAsync(int id, CreateUpdateCollectionDto input)
        {
            var collection = await _repository.GetAsync(id);
            ObjectMapper.Map(input, collection);
            collection = await _repository.UpdateAsync(collection, true);
            return ObjectMapper.Map<Collection, CollectionDto>(collection);
        }

        [Authorize(HolyBlessPermissions.Collections.Edit)]
        public async Task AddFilesToCollection(int collectionId, List<CollectionToFileDto> files)
        {
            var collection = await _repository.GetAsync(collectionId);
            foreach (var fileDto in files)
            {
                var existing = collection.CollectionToFiles.FirstOrDefault(x => x.FileId == fileDto.FileId);
                if (existing == null)
                {
                    collection.CollectionToFiles.Add(new CollectionToFile
                    {
                        CollectionId = collectionId,
                        FileId = fileDto.FileId,
                        Weight = fileDto.Weight
                    });
                }
                else
                {
                    existing.Weight = fileDto.Weight;
                }
            }
            await _repository.UpdateAsync(collection, true);
        }

        [Authorize(HolyBlessPermissions.Collections.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id, true);
        }

        /// <summary>
        /// Usage Admin page: Link a collection to a channel.
        /// </summary>
        /// <param name="channelId"></param>
        /// <param name="collectionId"></param>
        /// <returns></returns>
        //[Authorize(HolyBlessPermissions.Collections.Default)]
        public async Task LinkToChannel(int channelId, int collectionId)
        {
            var collection = await _repository.GetAsync(collectionId);
            collection.ChannelId = channelId;
            await _repository.UpdateAsync(collection, true);
        }

        /// <summary>
        /// [Admin] Usage Admin page: Link multiple collections to a channel.
        /// </summary>
        /// <param name="channelId"></param>
        /// <param name="collectionIds"></param>
        /// <returns></returns>
        //[Authorize(HolyBlessPermissions.Collections.Default)]
        public async Task LinkToChannel(int channelId, List<int> collectionIds)
        {
            var collections = await _repository.GetListAsync(x => collectionIds.Contains(x.Id));
            foreach (var collection in collections)
            {
                collection.ChannelId = channelId;
            }
            await _repository.UpdateManyAsync(collections, autoSave: true);
        }

        /// <summary>
        /// Admin page: Unlink a collection from a channel (Set ChannelId to null)
        /// </summary>
        /// <param name="collectionId"></param>
        /// <returns></returns>
        public async Task UnlinkFromChannel(int collectionId)
        {
            var collection = await _repository.GetAsync(collectionId);
            collection.ChannelId = null;
            await _repository.UpdateAsync(collection, true);
        }

        /// <summary>
        /// Admin page: Unlink multiple collections from their channels (Set ChannelId to null)
        /// </summary>
        /// <param name="collectionIds"></param>
        /// <returns></returns>
        public async Task UnlinkFromChannel(List<int> collectionIds)
        {
            var collections = await _repository.GetListAsync(x => collectionIds.Contains(x.Id));
            foreach (var collection in collections)
            {
                collection.ChannelId = null;
            }
            await _repository.UpdateManyAsync(collections, autoSave: true);
        }
/// <summary>
/// [Admin]
/// </summary>
/// <param name="input"></param>
/// <returns></returns>
        public async Task<PagedResultDto<CollectionDto>> GetListAsync(CollectionSearchDto input)
        {
            var queryable = await _repository.GetQueryableAsync();

            var query = queryable
                .WhereIf(input.Status.HasValue, x => x.Status == input.Status!.Value)
                .WhereIf(input.ChannelId.HasValue, x => x.ChannelId == input.ChannelId!.Value)
                .OrderBy(input.Sorting ?? "Weight");

            var collections = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount).Take(input.MaxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);

            return new PagedResultDto<CollectionDto>(
                totalCount,
                ObjectMapper.Map<List<Collection>, List<CollectionDto>>(collections)
            );
        }

        public async Task<PagedResultDto<CollectionArticleDto>> GetCollectionArticlesAsync(CollectionArticleSearchDto input)
        {
            var queryable = await _collectionToArticleRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.CollectionId == input.CollectionId)
                .WhereIf(input.Status.HasValue, x => x.Article.Status == input.Status!.Value)
                .OrderBy(input.Sorting ?? nameof(CollectionToArticle.Weight));

            var items = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount).Take(input.MaxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);

            var dtos = ObjectMapper.Map<List<CollectionToArticle>, List<CollectionArticleDto>>(items);
            return new PagedResultDto<CollectionArticleDto>(totalCount, dtos);
        }

        public async Task<PagedResultDto<CollectionToFileDto>> GetCollectionFilesAsync(CollectionFileSearchDto input)
        {
            var queryable = await _collectionToFileRepository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.CollectionId == input.CollectionId)
                .OrderBy(input.Sorting ?? nameof(CollectionToFile.Weight));

            var items = await AsyncExecuter.ToListAsync(
                query.Skip(input.SkipCount).Take(input.MaxResultCount)
            );
            var totalCount = await AsyncExecuter.CountAsync(query);

            var dtos = ObjectMapper.Map<List<CollectionToFile>, List<CollectionToFileDto>>(items);
            return new PagedResultDto<CollectionToFileDto>(totalCount, dtos);
        }

        /// <summary>
        /// [Admin] Get all collections without pagination, filtered by language code.
        /// </summary>
        /// <param name="languageCode">Optional language code to filter collections. If null, returns collections from all languages.</param>
        /// <returns>List of all collections matching the language criteria</returns>
        public async Task<List<CollectionDto>> GetAllCollectionsAsync(string? languageCode = null)
        {
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Include(x => x.Channel)
                .WhereIf(!string.IsNullOrEmpty(languageCode), x => x.LanguageCode != null && x.LanguageCode.ToLower() == languageCode!.ToLower())
                .OrderBy(x => x.Weight)
                ;
            var collections = await AsyncExecuter.ToListAsync(query);
            return ObjectMapper.Map<List<Collection>, List<CollectionDto>>(collections);
        }

        /// <summary>
        /// Move a collection to a new parent and reorder by weight relative to a beforeId.
        /// </summary>
        /// <param name="collectionId">The collection being moved</param>
        /// <param name="toParentId">The new parent collection id (nullable for root)</param>
        /// <param name="beforeId">The collection that the moved collection should be placed before. If null, places at the end.</param>
        public async Task MoveCollectionAsync(int collectionId, int? toParentId, int? beforeId)
        {
            var collectionEntity = await _repository.GetAsync(collectionId);

            // Determine the root collection ID to get the correct tree
            var rootCollectionId = collectionEntity.ParentCollectionId;
            while (rootCollectionId.HasValue)
            {
                var parentCollection = await _repository.GetAsync(rootCollectionId.Value);
                if (parentCollection.ParentCollectionId.HasValue)
                {
                    rootCollectionId = parentCollection.ParentCollectionId;
                }
                else
                {
                    break;
                }
            }

            // Use the collection itself as root if no parent found
            rootCollectionId = rootCollectionId ?? collectionId;

            var tree = await GetCollectionTreeAsync(rootCollectionId.Value);
            var allCollections = tree.Flatten(new List<CollectionTreeDto>());

            var collection = allCollections.FirstOrDefault(c => c.Id == collectionId);
            var toParent = toParentId.HasValue ? allCollections.FirstOrDefault(c => c.Id == toParentId.Value) : null;

            if (collection == null)
            {
                throw new UserFriendlyException(L["CollectionNotFound"]);
            }

            // Validation: Cannot move under collection's own child collection (deep check)
            if (toParent != null)
            {
                var descendantIds = new HashSet<int>(collection.GetDescendantIds());
                if (descendantIds.Contains(toParent.Id))
                {
                    throw new UserFriendlyException(L["CannotMoveCollectionUnderChildCollection"]);
                }
            }

            // Update ParentId logic
            collectionEntity.ParentCollectionId = toParentId;

            // Update Weight logic
            var queryable = await _repository.GetQueryableAsync();

            if (beforeId.HasValue)
            {
                // Normal case: insert before a specific collection
                var beforeEntity = await _repository.GetAsync(beforeId.Value);
                var insertWeight = beforeEntity.Weight;

                // Increase weight by +1 for siblings under the same parent of BeforeId with weight >= BeforeId's weight
                var siblingsToShift = await AsyncExecuter.ToListAsync(
                    queryable.Where(x => x.ParentCollectionId == beforeEntity.ParentCollectionId
                        && x.Id != collectionId // Exclude the collection being moved
                        && x.Weight >= insertWeight)
                );

                foreach (var s in siblingsToShift)
                {
                    s.Weight += 1;
                    await _repository.UpdateAsync(s, true);
                }

                // Set the moved collection's weight to the original weight of the 'before' collection
                collectionEntity.Weight = insertWeight;
            }
            else
            {
                // beforeId is null: move to the last position
                // Find the highest weight among siblings under the target parent
                var siblings = await AsyncExecuter.ToListAsync(
                    queryable.Where(x => x.ParentCollectionId == toParentId
                        && x.Id != collectionId) // Exclude the collection being moved
                );

                var maxWeight = siblings.Any() ? siblings.Max(x => x.Weight) : 0;
                collectionEntity.Weight = maxWeight + 1;
            }

            await _repository.UpdateAsync(collectionEntity, true);

            // Clear collection tree cache so next read rebuilds the snapshot
            await _treeSnapshotAppService.ClearCacheAsync(HolyBless.Enums.TreeType.Collection, rootCollectionId);
        }
    }
}