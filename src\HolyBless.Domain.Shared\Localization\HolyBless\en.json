{"Culture": "en", "Texts": {"CannotMoveUnderChildChannel": "Cannot move a channel under its own child channel.", "ChannelMissingLanguageCode": "Channel does not have a language code.", "ChannelNotFound": "Channel not found.", "VirtualFolderNotFound": "Virtual folder not found.", "CannotMoveVirtualFolderUnderChildFolder": "Cannot move a virtual folder under its own child folder.", "CannotDeleteVirtualFolderWithChildren": "Cannot delete virtual folder that has child folders. Please delete the child folders first.", "CannotDeleteVirtualFolderWithFiles": "Cannot delete virtual folder that has files. Please delete the files first.", "ChapterNotFound": "Chapter not found.", "CannotMoveChapterUnderChildChapter": "Cannot move a chapter under its own child chapter.", "AppName": "HolyBless", "Menu:HolyBless": "Book Store", "Menu:Books": "Books", "Actions": "Actions", "Close": "Close", "Delete": "Delete", "Edit": "Edit", "PublishDate": "Publish date", "NewBook": "New book", "Name": "Name", "Type": "Type", "Price": "Price", "CreationTime": "Creation time", "AreYouSure": "Are you sure?", "AreYouSureToDelete": "Are you sure you want to delete this item?", "BookDeletionConfirmationMessage": "Are you sure to delete the book '{0}'?", "SuccessfullyDeleted": "Successfully deleted!", "Enum:BookType.0": "Undefined", "Enum:BookType.1": "Adventure", "Enum:BookType.2": "Biography", "Enum:BookType.3": "Dystopia", "Enum:BookType.4": "Fantastic", "Enum:BookType.5": "Horror", "Enum:BookType.6": "Science", "Enum:BookType.7": "Science fiction", "Enum:BookType.8": "Poetry", "Permission:HolyBless": "Book Store", "Permission:Books": "Book Management", "Permission:Books.Create": "Creating new books", "Permission:Books.Edit": "Editing the books", "Permission:Books.Delete": "Deleting the books", "Permission:CollectionArticles": "Collection Article Management", "Permission:CollectionArticles.Create": "Creating collection-article relationships", "Permission:CollectionArticles.Edit": "Editing collection-article relationships", "Permission:CollectionArticles.Delete": "Deleting collection-article relationships", "Menu:Home": "Home", "LongWelcomeMessage": "Welcome to the application. This is a startup project based on the ABP framework. For more information visit", "Welcome": "Welcome", "CannotDeleteChannelWithCollections": "Cannot delete channel that has collections. Please delete the collections first.", "CannotDeleteChannelWithEBooks": "Cannot delete channel that has e-books. Please delete the e-books first.", "CannotDeleteChannelWithAlbums": "Cannot delete channel that has albums. Please delete the albums first.", "CannotDeleteChannelWithVirtualFolders": "Cannot delete channel that has virtual disk folders. Please delete the folders first.", "CannotDeleteChannelWithChildChannels": "Cannot delete channel that has child channels. Please delete the child channels first.", "FileAlreadyAttachedToArticle": "This file is already attached to the article.", "ArticleAlreadyLinkedToCollection": "This article is already linked to the collection.", "UserRegistrationNotAvailable": "User registration is not available.", "InvalidTagIds": "Invalid tag IDs: {0}"}}